# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Services/Order/NafaPlace.Order.API/NafaPlace.Order.API.csproj", "Services/Order/NafaPlace.Order.API/"]
COPY ["src/Services/Order/NafaPlace.Order.Application/NafaPlace.Order.Application.csproj", "Services/Order/NafaPlace.Order.Application/"]
COPY ["src/Services/Order/NafaPlace.Order.Domain/NafaPlace.Order.Domain.csproj", "Services/Order/NafaPlace.Order.Domain/"]
COPY ["src/Services/Order/NafaPlace.Order.Infrastructure/NafaPlace.Order.Infrastructure.csproj", "Services/Order/NafaPlace.Order.Infrastructure/"]
RUN dotnet restore "Services/Order/NafaPlace.Order.API/NafaPlace.Order.API.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Services/Order/", "Services/Order/"]
WORKDIR "/src/Services/Order/NafaPlace.Order.API"
RUN dotnet build "NafaPlace.Order.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Order.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Configuration pour le marché africain
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Conakry
ENV LANG=fr_GN.UTF-8
ENV LANGUAGE=fr_GN.UTF-8
ENV LC_ALL=fr_GN.UTF-8

EXPOSE 8080
ENTRYPOINT ["dotnet", "NafaPlace.Order.API.dll"]