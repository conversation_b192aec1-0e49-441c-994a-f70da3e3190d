#!/usr/bin/env pwsh

# Script de déploiement fiable pour NafaPlace en production
# Auteur: NafaPlace Team
# Version: 2.0

param(
    [switch]$SkipTests = $false,
    [switch]$Force = $false,
    [string]$Environment = "production"
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Déploiement NafaPlace en Production" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Skip Tests: $SkipTests" -ForegroundColor Yellow
Write-Host "Force Deploy: $Force" -ForegroundColor Yellow
Write-Host ""

# Vérification des prérequis
Write-Host "🔍 Vérification des prérequis..." -ForegroundColor Blue

# Vérifier flyctl
try {
    $flyVersion = flyctl version
    Write-Host "✅ Fly CLI: $flyVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Fly CLI non trouvé. Installez-le depuis https://fly.io/docs/getting-started/installing-flyctl/" -ForegroundColor Red
    exit 1
}

# Vérifier l'authentification Fly
try {
    $flyAuth = flyctl auth whoami
    Write-Host "✅ Authentifié sur Fly.io: $flyAuth" -ForegroundColor Green
} catch {
    Write-Host "❌ Non authentifié sur Fly.io. Exécutez: flyctl auth login" -ForegroundColor Red
    exit 1
}

# Tests (si non ignorés)
if (-not $SkipTests) {
    Write-Host "🧪 Exécution des tests..." -ForegroundColor Blue
    try {
        dotnet test --configuration Release --verbosity minimal
        Write-Host "✅ Tests réussis" -ForegroundColor Green
    } catch {
        Write-Host "❌ Tests échoués" -ForegroundColor Red
        if (-not $Force) {
            exit 1
        }
        Write-Host "⚠️ Déploiement forcé malgré les tests échoués" -ForegroundColor Yellow
    }
}

# Ordre de déploiement des services
$services = @(
    @{ Name = "Identity API"; Config = "fly-identity.toml"; App = "nafaplace-identity-api" },
    @{ Name = "Catalog API"; Config = "fly-catalog.toml"; App = "nafaplace-catalog-api" },
    @{ Name = "Cart API"; Config = "fly-cart.toml"; App = "nafaplace-cart-api" },
    @{ Name = "Order API"; Config = "fly-order.toml"; App = "nafaplace-order-api" },
    @{ Name = "Payment API"; Config = "fly-payment.toml"; App = "nafaplace-payment-api" },
    @{ Name = "Reviews API"; Config = "fly-reviews.toml"; App = "nafaplace-reviews-api" },
    @{ Name = "Notifications API"; Config = "fly-notifications.toml"; App = "nafaplace-notifications-api" },
    @{ Name = "API Gateway"; Config = "fly.toml"; App = "nafaplace-api-gateway" },
    @{ Name = "Web App"; Config = "fly-web.toml"; App = "nafaplace-web" },
    @{ Name = "Admin Portal"; Config = "fly-admin-portal.toml"; App = "nafaplace-admin-portal" },
    @{ Name = "Seller Portal"; Config = "fly-seller-portal.toml"; App = "nafaplace-seller-portal" }
)

$deployedServices = @()
$failedServices = @()

# Déploiement des services
Write-Host "🚀 Déploiement des services..." -ForegroundColor Blue

foreach ($service in $services) {
    Write-Host ""
    Write-Host "📦 Déploiement de $($service.Name)..." -ForegroundColor Cyan
    
    try {
        # Déploiement
        flyctl deploy --config $service.Config --app $service.App --wait-timeout 300
        
        # Vérification de santé
        Write-Host "🏥 Vérification de santé de $($service.Name)..." -ForegroundColor Yellow
        Start-Sleep -Seconds 10
        
        $status = flyctl status --app $service.App --json | ConvertFrom-Json
        if ($status.Machines[0].State -eq "started") {
            Write-Host "✅ $($service.Name) déployé avec succès" -ForegroundColor Green
            $deployedServices += $service.Name
        } else {
            throw "Service non démarré: $($status.Machines[0].State)"
        }
        
    } catch {
        Write-Host "❌ Échec du déploiement de $($service.Name): $($_.Exception.Message)" -ForegroundColor Red
        $failedServices += $service.Name
        
        if (-not $Force) {
            Write-Host "🔄 Rollback des services déployés..." -ForegroundColor Yellow
            # Ici on pourrait implémenter un rollback automatique
            exit 1
        }
    }
}

# Résumé du déploiement
Write-Host ""
Write-Host "📊 Résumé du déploiement:" -ForegroundColor Blue
Write-Host "✅ Services déployés avec succès: $($deployedServices.Count)" -ForegroundColor Green
foreach ($service in $deployedServices) {
    Write-Host "  - $service" -ForegroundColor Green
}

if ($failedServices.Count -gt 0) {
    Write-Host "❌ Services échoués: $($failedServices.Count)" -ForegroundColor Red
    foreach ($service in $failedServices) {
        Write-Host "  - $service" -ForegroundColor Red
    }
}

# Tests post-déploiement
Write-Host ""
Write-Host "🔍 Tests post-déploiement..." -ForegroundColor Blue

$endpoints = @(
    "https://nafaplace-api-gateway.fly.dev/health",
    "https://nafaplace-web.fly.dev/"
)

foreach ($endpoint in $endpoints) {
    try {
        $response = Invoke-WebRequest -Uri $endpoint -TimeoutSec 30
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $endpoint - OK" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $endpoint - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $endpoint - Erreur: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
if ($failedServices.Count -eq 0) {
    Write-Host "🎉 Déploiement terminé avec succès!" -ForegroundColor Green
    Write-Host "🌐 Application disponible sur: https://nafaplace-web.fly.dev" -ForegroundColor Cyan
} else {
    Write-Host "⚠️ Déploiement terminé avec des erreurs" -ForegroundColor Yellow
    exit 1
}
