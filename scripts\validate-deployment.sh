#!/bin/bash

# Script de validation du déploiement NafaPlace
ENVIRONMENT=${1:-production}

echo "🔍 Validation du déploiement NafaPlace"
echo "Environment: $ENVIRONMENT"
echo ""

errors=0

# Vérifier Fly CLI
echo "🔧 Vérification des outils..."
if command -v flyctl &> /dev/null; then
    flyVersion=$(flyctl version)
    echo "✅ Fly CLI installé: $flyVersion"
else
    echo "❌ Fly CLI non trouvé"
    ((errors++))
fi

# Vérifier Docker
if command -v docker &> /dev/null; then
    dockerVersion=$(docker --version)
    echo "✅ Docker installé: $dockerVersion"
else
    echo "❌ Docker non trouvé"
    ((errors++))
fi

# Vérifier .NET
if command -v dotnet &> /dev/null; then
    dotnetVersion=$(dotnet --version)
    echo "✅ .NET SDK installé: $dotnetVersion"
else
    echo "❌ .NET SDK non trouvé"
    ((errors++))
fi

# Vérifier l'authentification Fly.io
echo ""
echo "🔐 Vérification de l'authentification..."
if flyctl auth whoami &> /dev/null; then
    flyAuth=$(flyctl auth whoami)
    echo "✅ Authentifié sur Fly.io: $flyAuth"
else
    echo "❌ Non authentifié sur Fly.io. Exécutez: flyctl auth login"
    ((errors++))
fi

# Vérifier les configurations
echo ""
echo "📁 Vérification des configurations..."
configs=("fly.toml" "fly-catalog.toml" "fly-identity.toml" "fly-cart.toml" "fly-order.toml" "fly-payment.toml")
for config in "${configs[@]}"; do
    if [ -f "$config" ]; then
        echo "✅ Configuration trouvée: $config"
    else
        echo "❌ Configuration manquante: $config"
        ((errors++))
    fi
done

# Vérifier les variables d'environnement
echo ""
echo "🌍 Vérification des variables d'environnement..."
if [ -n "$STRIPE_SECRET_KEY" ]; then
    echo "✅ STRIPE_SECRET_KEY configurée"
else
    echo "⚠️ STRIPE_SECRET_KEY non configurée (placeholder sera utilisé)"
fi

if [ -n "$STRIPE_WEBHOOK_SECRET" ]; then
    echo "✅ STRIPE_WEBHOOK_SECRET configurée"
else
    echo "⚠️ STRIPE_WEBHOOK_SECRET non configurée (placeholder sera utilisé)"
fi

if [ -n "$CLOUDINARY_URL" ]; then
    echo "✅ CLOUDINARY_URL configurée"
else
    echo "⚠️ CLOUDINARY_URL non configurée (placeholder sera utilisé)"
fi

# Résumé
echo ""
echo "📊 Résumé de la validation"
echo "========================="

if [ $errors -eq 0 ]; then
    echo "🎉 Validation réussie! Prêt pour le déploiement."
    echo ""
    echo "📋 Prochaines étapes:"
    echo "1. ./scripts/setup-production-databases.sh $ENVIRONMENT"
    echo "2. ./scripts/setup-fly-secrets-v2.sh $ENVIRONMENT"
    echo "3. ./scripts/deploy-production.sh"
    exit 0
else
    echo "❌ Validation échouée! $errors erreur(s) trouvée(s)."
    echo "Corrigez les erreurs avant de continuer."
    exit 1
fi
