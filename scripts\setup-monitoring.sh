#!/bin/bash

# Script de configuration du monitoring et des alertes pour NafaPlace
# Auteur: NafaPlace Team
# Version: 1.0

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Paramètres
ENVIRONMENT=${1:-production}
WEBHOOK_URL=${2:-""}

if [ "$ENVIRONMENT" = "staging" ]; then
    APP_SUFFIX="-staging"
else
    APP_SUFFIX=""
fi

echo -e "${GREEN}📊 Configuration du monitoring NafaPlace${NC}"
echo -e "${YELLOW}Environment: $ENVIRONMENT${NC}"
echo ""

# Vérifier flyctl
if ! command -v flyctl &> /dev/null; then
    echo -e "${RED}❌ Fly CLI non trouvé. Installez-le depuis https://fly.io/docs/getting-started/installing-flyctl/${NC}"
    exit 1
fi

# Vérifier l'authentification
if ! flyctl auth whoami &> /dev/null; then
    echo -e "${RED}❌ Non authentifié sur Fly.io. Exécutez: flyctl auth login${NC}"
    exit 1
fi

echo -e "${BLUE}🔍 Configuration des health checks...${NC}"

# Liste des applications à monitorer
apps=(
    "nafaplace-api-gateway$APP_SUFFIX"
    "nafaplace-identity-api$APP_SUFFIX"
    "nafaplace-catalog-api$APP_SUFFIX"
    "nafaplace-cart-api$APP_SUFFIX"
    "nafaplace-order-api$APP_SUFFIX"
    "nafaplace-payment-api$APP_SUFFIX"
    "nafaplace-reviews-api$APP_SUFFIX"
    "nafaplace-notifications-api$APP_SUFFIX"
    "nafaplace-web$APP_SUFFIX"
    "nafaplace-admin-portal$APP_SUFFIX"
    "nafaplace-seller-portal$APP_SUFFIX"
)

# Fonction pour configurer les health checks
configure_health_checks() {
    local app_name=$1
    
    echo -e "${CYAN}🏥 Configuration health check pour $app_name...${NC}"
    
    if flyctl apps list | grep -q "$app_name"; then
        # Les health checks sont déjà configurés dans les fly.toml
        echo -e "${GREEN}✅ Health check configuré pour $app_name${NC}"
    else
        echo -e "${YELLOW}⚠️ Application $app_name non trouvée${NC}"
    fi
}

# Configurer les health checks pour toutes les applications
for app in "${apps[@]}"; do
    configure_health_checks "$app"
done

echo ""
echo -e "${BLUE}📈 Configuration des métriques...${NC}"

# Créer un script de monitoring personnalisé
cat > monitoring-check.sh << 'EOF'
#!/bin/bash

# Script de vérification de santé NafaPlace
# Exécuté périodiquement pour vérifier l'état des services

ENVIRONMENT=${1:-production}
WEBHOOK_URL=${2:-""}

if [ "$ENVIRONMENT" = "staging" ]; then
    APP_SUFFIX="-staging"
else
    APP_SUFFIX=""
fi

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Fonction pour envoyer une alerte
send_alert() {
    local message=$1
    local severity=$2
    
    echo -e "${RED}🚨 ALERTE: $message${NC}"
    
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"🚨 NafaPlace Alert [$severity]: $message\"}" \
            > /dev/null 2>&1 || true
    fi
}

# Fonction pour vérifier un service
check_service() {
    local app_name=$1
    local service_name=$2
    
    echo -n "Vérification de $service_name... "
    
    if flyctl status --app "$app_name" | grep -q "started"; then
        echo -e "${GREEN}✅ OK${NC}"
        return 0
    else
        echo -e "${RED}❌ ERREUR${NC}"
        send_alert "$service_name est en panne" "CRITICAL"
        return 1
    fi
}

# Fonction pour vérifier les métriques
check_metrics() {
    local app_name=$1
    local service_name=$2
    
    # Vérifier l'utilisation CPU/RAM
    local metrics=$(flyctl metrics --app "$app_name" --region cdg 2>/dev/null || echo "")
    
    if [ -n "$metrics" ]; then
        echo -e "${GREEN}📊 Métriques collectées pour $service_name${NC}"
    else
        echo -e "${YELLOW}⚠️ Pas de métriques pour $service_name${NC}"
    fi
}

echo "🔍 Vérification de santé NafaPlace - $(date)"
echo "=============================================="

failed_services=0

# Vérifier tous les services
services=(
    "nafaplace-api-gateway$APP_SUFFIX:API Gateway"
    "nafaplace-identity-api$APP_SUFFIX:Identity API"
    "nafaplace-catalog-api$APP_SUFFIX:Catalog API"
    "nafaplace-cart-api$APP_SUFFIX:Cart API"
    "nafaplace-order-api$APP_SUFFIX:Order API"
    "nafaplace-payment-api$APP_SUFFIX:Payment API"
    "nafaplace-reviews-api$APP_SUFFIX:Reviews API"
    "nafaplace-notifications-api$APP_SUFFIX:Notifications API"
    "nafaplace-web$APP_SUFFIX:Web App"
    "nafaplace-admin-portal$APP_SUFFIX:Admin Portal"
    "nafaplace-seller-portal$APP_SUFFIX:Seller Portal"
)

for service_info in "${services[@]}"; do
    IFS=':' read -r app_name service_name <<< "$service_info"
    
    if ! check_service "$app_name" "$service_name"; then
        ((failed_services++))
    fi
    
    check_metrics "$app_name" "$service_name"
done

echo ""
echo "=============================================="

if [ $failed_services -eq 0 ]; then
    echo -e "${GREEN}✅ Tous les services sont opérationnels${NC}"
else
    echo -e "${RED}❌ $failed_services service(s) en panne${NC}"
    send_alert "$failed_services service(s) NafaPlace en panne" "CRITICAL"
fi

echo "Vérification terminée - $(date)"
EOF

chmod +x monitoring-check.sh

echo -e "${GREEN}✅ Script de monitoring créé: monitoring-check.sh${NC}"

echo ""
echo -e "${BLUE}⏰ Configuration des tâches périodiques...${NC}"

# Créer un script cron pour le monitoring automatique
cat > setup-cron.sh << 'EOF'
#!/bin/bash

# Ajouter une tâche cron pour le monitoring (toutes les 5 minutes)
CRON_JOB="*/5 * * * * /path/to/nafaplace/monitoring-check.sh production"

# Vérifier si la tâche existe déjà
if ! crontab -l 2>/dev/null | grep -q "monitoring-check.sh"; then
    # Ajouter la tâche cron
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    echo "✅ Tâche cron ajoutée pour le monitoring automatique"
else
    echo "⚠️ Tâche cron déjà configurée"
fi
EOF

chmod +x setup-cron.sh

echo -e "${GREEN}✅ Script de configuration cron créé: setup-cron.sh${NC}"

echo ""
echo -e "${BLUE}📋 Configuration des logs centralisés...${NC}"

# Créer un script pour collecter les logs
cat > collect-logs.sh << 'EOF'
#!/bin/bash

# Script de collecte des logs NafaPlace
ENVIRONMENT=${1:-production}
DAYS=${2:-1}

if [ "$ENVIRONMENT" = "staging" ]; then
    APP_SUFFIX="-staging"
else
    APP_SUFFIX=""
fi

echo "📋 Collecte des logs NafaPlace ($ENVIRONMENT) - Derniers $DAYS jour(s)"
echo "=================================================================="

apps=(
    "nafaplace-api-gateway$APP_SUFFIX"
    "nafaplace-identity-api$APP_SUFFIX"
    "nafaplace-catalog-api$APP_SUFFIX"
    "nafaplace-cart-api$APP_SUFFIX"
    "nafaplace-order-api$APP_SUFFIX"
    "nafaplace-payment-api$APP_SUFFIX"
    "nafaplace-reviews-api$APP_SUFFIX"
    "nafaplace-notifications-api$APP_SUFFIX"
    "nafaplace-web$APP_SUFFIX"
)

for app in "${apps[@]}"; do
    echo ""
    echo "📄 Logs pour $app:"
    echo "-------------------"
    flyctl logs --app "$app" --since "${DAYS}d" | tail -20
done
EOF

chmod +x collect-logs.sh

echo -e "${GREEN}✅ Script de collecte des logs créé: collect-logs.sh${NC}"

echo ""
echo -e "${GREEN}✅ Configuration du monitoring terminée!${NC}"
echo ""
echo -e "${BLUE}📋 Scripts créés:${NC}"
echo "  - monitoring-check.sh : Vérification de santé manuelle"
echo "  - setup-cron.sh : Configuration du monitoring automatique"
echo "  - collect-logs.sh : Collecte des logs"

echo ""
echo -e "${BLUE}📋 Utilisation:${NC}"
echo "1. Test manuel: ./monitoring-check.sh $ENVIRONMENT"
echo "2. Monitoring auto: ./setup-cron.sh"
echo "3. Collecte logs: ./collect-logs.sh $ENVIRONMENT 7"

echo ""
echo -e "${YELLOW}⚠️ Pour les alertes, configurez WEBHOOK_URL:${NC}"
echo "export WEBHOOK_URL=\"https://hooks.slack.com/services/...\""
echo "./monitoring-check.sh $ENVIRONMENT \$WEBHOOK_URL"
