#!/usr/bin/env pwsh

# Script pour corriger les Dockerfiles de NafaPlace
# Standardise les ports, variables d'environnement et optimise les builds

$ErrorActionPreference = "Stop"

Write-Host "🔧 Correction des Dockerfiles NafaPlace" -ForegroundColor Green
Write-Host ""

# Liste des Dockerfiles à corriger
$dockerfiles = @(
    "src/Services/Catalog/NafaPlace.Catalog.API/Dockerfile",
    "src/Services/Identity/NafaPlace.Identity.API/Dockerfile",
    "src/Services/Cart/NafaPlace.Cart.API/Dockerfile",
    "src/Services/Order/NafaPlace.Order.API/Dockerfile",
    "src/Services/Payment/NafaPlace.Payment.API/Dockerfile",
    "src/Services/Reviews/NafaPlace.Reviews.API/Dockerfile",
    "src/Services/Notifications/NafaPlace.Notifications.API/Dockerfile"
)

foreach ($dockerfile in $dockerfiles) {
    if (Test-Path $dockerfile) {
        Write-Host "🔄 Correction de $dockerfile..." -ForegroundColor Yellow
        
        # Lire le contenu du fichier
        $content = Get-Content $dockerfile -Raw
        
        # Corrections standardisées
        $content = $content -replace 'EXPOSE 80', 'EXPOSE 8080'
        $content = $content -replace 'ENV TZ=Africa/Dakar', 'ENV TZ=Africa/Conakry'
        $content = $content -replace 'ENV LANG=fr_FR\.UTF-8', 'ENV LANG=fr_GN.UTF-8'
        $content = $content -replace 'ENV LANGUAGE=fr_FR\.UTF-8', 'ENV LANGUAGE=fr_GN.UTF-8'
        $content = $content -replace 'ENV LC_ALL=fr_FR\.UTF-8', 'ENV LC_ALL=fr_GN.UTF-8'
        
        # Ajouter les optimisations de build si pas présentes
        if ($content -notmatch '--no-cache') {
            $content = $content -replace 'RUN dotnet restore', 'RUN dotnet restore --no-cache'
        }
        
        # Écrire le contenu corrigé
        Set-Content -Path $dockerfile -Value $content -NoNewline
        
        Write-Host "✅ $dockerfile corrigé" -ForegroundColor Green
    } else {
        Write-Host "⚠️ $dockerfile non trouvé" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎉 Correction des Dockerfiles terminée!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Changements appliqués:"
Write-Host "  - Port standardisé: 8080"
Write-Host "  - Timezone: Africa/Conakry"
Write-Host "  - Locale: fr_GN.UTF-8"
Write-Host "  - Optimisations de build ajoutées"
