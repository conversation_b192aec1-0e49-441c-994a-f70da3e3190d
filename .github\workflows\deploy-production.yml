name: Deploy to Production

on:
  push:
    branches: [ main ]
  release:
    types: [ published ]

env:
  FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

jobs:
  security-scan:
    runs-on: ubuntu-latest
    continue-on-error: true
    steps:
      - uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        continue-on-error: true
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        continue-on-error: true
        with:
          sarif_file: 'trivy-results.sarif'

  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '9.0.x'
      
      - name: Restore dependencies
        run: dotnet restore
      
      - name: Build
        run: dotnet build --no-restore --configuration Release
      
      - name: Test
        run: dotnet test --no-build --configuration Release --verbosity normal

  deploy-production:
    runs-on: ubuntu-latest
    needs: [security-scan, test]
    environment: production

    steps:
      - uses: actions/checkout@v4

      - uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Deploy Services to Production
        run: |
          # Ordre de déploiement des services
          services=(
            "nafaplace-identity-api:fly-identity.toml"
            "nafaplace-catalog-api:fly-catalog.toml"
            "nafaplace-cart-api:fly-cart.toml"
            "nafaplace-order-api:fly-order.toml"
            "nafaplace-payment-api:fly-payment.toml"
            "nafaplace-reviews-api:fly-reviews.toml"
            "nafaplace-notifications-api:fly-notifications.toml"
            "nafaplace-api-gateway:fly.toml"
            "nafaplace-web:fly-web.toml"
            "nafaplace-admin-portal:fly-admin-portal.toml"
            "nafaplace-seller-portal:fly-seller-portal.toml"
          )

          failed_services=()

          for service_info in "${services[@]}"; do
            IFS=':' read -r app_name config_file <<< "$service_info"
            echo "🚀 Deploying $app_name..."

            if flyctl deploy --config "$config_file" --app "$app_name" --wait-timeout 300; then
              echo "✅ $app_name deployed successfully"

              # Health check
              echo "🏥 Health check for $app_name..."
              sleep 10

              if flyctl status --app "$app_name" | grep -q "started"; then
                echo "✅ $app_name is healthy"
              else
                echo "❌ $app_name health check failed"
                failed_services+=("$app_name")
              fi
            else
              echo "❌ Failed to deploy $app_name"
              failed_services+=("$app_name")
            fi
          done

          if [ ${#failed_services[@]} -gt 0 ]; then
            echo "❌ Failed services: ${failed_services[*]}"
            exit 1
          fi

          echo "🎉 All services deployed successfully!"
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Post-deployment Tests
        run: |
          echo "🔍 Running post-deployment tests..."

          endpoints=(
            "https://nafaplace-api-gateway.fly.dev/health"
            "https://nafaplace-web.fly.dev/"
          )

          for endpoint in "${endpoints[@]}"; do
            if curl -s --max-time 30 "$endpoint" > /dev/null; then
              echo "✅ $endpoint - OK"
            else
              echo "❌ $endpoint - Failed"
              exit 1
            fi
          done

          echo "✅ All post-deployment tests passed!"

      - name: Notify Success
        if: success()
        run: |
          echo "🎉 Production deployment completed successfully!"
          echo "🌐 Application available at: https://nafaplace-web.fly.dev"

      - name: Notify Failure
        if: failure()
        run: |
          echo "❌ Production deployment failed!"
          echo "Please check the logs and consider rollback if necessary."
      
      - name: Deploy Catalog API to Production
        run: |
          # Update Cloudinary secrets before deployment
          flyctl secrets set Cloudinary__CloudName="${{ secrets.CLOUDINARY_CLOUD_NAME }}" --app nafaplace-catalog-api
          flyctl secrets set Cloudinary__ApiKey="${{ secrets.CLOUDINARY_API_KEY }}" --app nafaplace-catalog-api
          flyctl secrets set Cloudinary__ApiSecret="${{ secrets.CLOUDINARY_API_SECRET }}" --app nafaplace-catalog-api
          flyctl deploy --config fly-catalog.toml --app nafaplace-catalog-api
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Deploy Payment API to Production
        run: |
          # Update Stripe secrets before deployment
          flyctl secrets set STRIPE_SECRET_KEY="${{ secrets.STRIPE_SECRET_KEY }}" --app nafaplace-payment-api
          flyctl secrets set STRIPE_PUBLISHABLE_KEY="${{ secrets.STRIPE_PUBLISHABLE_KEY }}" --app nafaplace-payment-api
          flyctl deploy --config fly-payment.toml --app nafaplace-payment-api
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Deploy Web App to Production
        run: |
          flyctl deploy --config fly-web.toml --app nafaplace-web
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
      
      - name: Run Health Checks
        run: |
          sleep 30
          curl -f https://nafaplace-api-gateway.fly.dev/health || exit 1
          curl -f https://nafaplace-web.fly.dev/ || exit 1
      
      - name: Notify Deployment Success
        if: success()
        run: |
          echo "✅ Production deployment successful!"
          echo "🌐 Web App: https://nafaplace-web.fly.dev"
          echo "🔗 API Gateway: https://nafaplace-api-gateway.fly.dev"
