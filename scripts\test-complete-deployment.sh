#!/bin/bash

# Script de test complet du déploiement NafaPlace
# Effectue un déploiement de test et valide tous les services
# Auteur: NafaPlace Team
# Version: 1.0

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Paramètres
ENVIRONMENT=${1:-staging}
SKIP_BUILD=${2:-false}

if [ "$ENVIRONMENT" = "staging" ]; then
    APP_SUFFIX="-staging"
else
    APP_SUFFIX=""
fi

echo -e "${GREEN}🧪 Test complet du déploiement NafaPlace${NC}"
echo -e "${YELLOW}Environment: $ENVIRONMENT${NC}"
echo -e "${YELLOW}Skip Build: $SKIP_BUILD${NC}"
echo ""

# Vérifier les prérequis
echo -e "${BLUE}🔍 Vérification des prérequis...${NC}"

if ! command -v flyctl &> /dev/null; then
    echo -e "${RED}❌ Fly CLI non trouvé${NC}"
    exit 1
fi

if ! flyctl auth whoami &> /dev/null; then
    echo -e "${RED}❌ Non authentifié sur Fly.io${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prérequis validés${NC}"

# Variables de test
test_results=()
failed_tests=0
total_tests=0

# Fonction pour enregistrer un résultat de test
record_test() {
    local test_name=$1
    local result=$2
    local message=$3
    
    ((total_tests++))
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✅ $test_name: $message${NC}"
        test_results+=("✅ $test_name: $message")
    elif [ "$result" = "WARN" ]; then
        echo -e "${YELLOW}⚠️ $test_name: $message${NC}"
        test_results+=("⚠️ $test_name: $message")
    else
        echo -e "${RED}❌ $test_name: $message${NC}"
        test_results+=("❌ $test_name: $message")
        ((failed_tests++))
    fi
}

# Phase 1: Validation pré-déploiement
echo ""
echo -e "${PURPLE}📋 Phase 1: Validation pré-déploiement${NC}"
echo "========================================"

# Test 1: Vérifier les configurations
echo -e "${CYAN}Test 1: Configurations Fly.toml${NC}"
configs=(
    "fly.toml"
    "fly-catalog.toml"
    "fly-identity.toml"
    "fly-cart.toml"
    "fly-order.toml"
    "fly-payment.toml"
    "fly-reviews.toml"
    "fly-notifications.toml"
)

config_errors=0
for config in "${configs[@]}"; do
    if [ -f "$config" ]; then
        if grep -q "internal_port = 8080" "$config"; then
            record_test "Config-$config" "PASS" "Port 8080 configuré"
        else
            record_test "Config-$config" "FAIL" "Port 8080 non configuré"
            ((config_errors++))
        fi
    else
        record_test "Config-$config" "FAIL" "Fichier manquant"
        ((config_errors++))
    fi
done

# Test 2: Compilation
if [ "$SKIP_BUILD" != "true" ]; then
    echo ""
    echo -e "${CYAN}Test 2: Compilation${NC}"
    if dotnet build --configuration Release --verbosity quiet; then
        record_test "Build" "PASS" "Compilation réussie"
    else
        record_test "Build" "FAIL" "Échec de compilation"
    fi
fi

# Phase 2: Déploiement des services
echo ""
echo -e "${PURPLE}📋 Phase 2: Déploiement des services${NC}"
echo "====================================="

# Services à déployer dans l'ordre
services=(
    "nafaplace-identity-api$APP_SUFFIX:fly-identity.toml:Identity API"
    "nafaplace-catalog-api$APP_SUFFIX:fly-catalog.toml:Catalog API"
    "nafaplace-cart-api$APP_SUFFIX:fly-cart.toml:Cart API"
    "nafaplace-order-api$APP_SUFFIX:fly-order.toml:Order API"
    "nafaplace-payment-api$APP_SUFFIX:fly-payment.toml:Payment API"
    "nafaplace-reviews-api$APP_SUFFIX:fly-reviews.toml:Reviews API"
    "nafaplace-notifications-api$APP_SUFFIX:fly-notifications.toml:Notifications API"
    "nafaplace-api-gateway$APP_SUFFIX:fly.toml:API Gateway"
)

# Test 3: Déploiement des services
for service_info in "${services[@]}"; do
    IFS=':' read -r app_name config_file service_name <<< "$service_info"
    
    echo ""
    echo -e "${CYAN}Test 3: Déploiement $service_name${NC}"
    
    if [ -f "$config_file" ]; then
        if flyctl deploy --config "$config_file" --app "$app_name" --wait-timeout 300; then
            record_test "Deploy-$service_name" "PASS" "Déploiement réussi"
            
            # Attendre que le service soit prêt
            sleep 15
            
            # Vérifier le statut
            if flyctl status --app "$app_name" | grep -q "started"; then
                record_test "Status-$service_name" "PASS" "Service démarré"
            else
                record_test "Status-$service_name" "WARN" "Service pas encore démarré"
            fi
        else
            record_test "Deploy-$service_name" "FAIL" "Échec du déploiement"
        fi
    else
        record_test "Deploy-$service_name" "FAIL" "Configuration $config_file manquante"
    fi
done

# Phase 3: Tests de santé
echo ""
echo -e "${PURPLE}📋 Phase 3: Tests de santé${NC}"
echo "============================"

# Test 4: Health checks
health_endpoints=(
    "https://nafaplace-api-gateway$APP_SUFFIX.fly.dev/health:API Gateway"
    "https://nafaplace-identity-api$APP_SUFFIX.fly.dev/health:Identity API"
    "https://nafaplace-catalog-api$APP_SUFFIX.fly.dev/health:Catalog API"
    "https://nafaplace-cart-api$APP_SUFFIX.fly.dev/health:Cart API"
    "https://nafaplace-order-api$APP_SUFFIX.fly.dev/health:Order API"
    "https://nafaplace-payment-api$APP_SUFFIX.fly.dev/health:Payment API"
    "https://nafaplace-reviews-api$APP_SUFFIX.fly.dev/health:Reviews API"
    "https://nafaplace-notifications-api$APP_SUFFIX.fly.dev/health:Notifications API"
)

echo -e "${CYAN}Test 4: Health Checks${NC}"
for endpoint_info in "${health_endpoints[@]}"; do
    IFS=':' read -r endpoint service_name <<< "$endpoint_info"
    
    echo -n "Vérification $service_name... "
    
    if curl -s --max-time 30 "$endpoint" > /dev/null 2>&1; then
        record_test "Health-$service_name" "PASS" "Endpoint accessible"
    else
        record_test "Health-$service_name" "FAIL" "Endpoint non accessible"
    fi
done

# Test 5: Connectivité inter-services
echo ""
echo -e "${CYAN}Test 5: Connectivité inter-services${NC}"

# Tester l'API Gateway
if curl -s --max-time 30 "https://nafaplace-api-gateway$APP_SUFFIX.fly.dev/health" | grep -q "Healthy"; then
    record_test "Gateway-Connectivity" "PASS" "API Gateway répond correctement"
else
    record_test "Gateway-Connectivity" "WARN" "API Gateway réponse suspecte"
fi

# Phase 4: Tests fonctionnels
echo ""
echo -e "${PURPLE}📋 Phase 4: Tests fonctionnels${NC}"
echo "==============================="

# Test 6: Endpoints principaux
echo -e "${CYAN}Test 6: Endpoints principaux${NC}"

main_endpoints=(
    "https://nafaplace-api-gateway$APP_SUFFIX.fly.dev:API Gateway"
)

for endpoint_info in "${main_endpoints[@]}"; do
    IFS=':' read -r endpoint service_name <<< "$endpoint_info"
    
    if curl -s --max-time 30 "$endpoint" > /dev/null 2>&1; then
        record_test "Endpoint-$service_name" "PASS" "Endpoint principal accessible"
    else
        record_test "Endpoint-$service_name" "FAIL" "Endpoint principal non accessible"
    fi
done

# Rapport final
echo ""
echo -e "${PURPLE}📊 Rapport de test final${NC}"
echo "========================="

echo ""
echo -e "${BLUE}📋 Résumé des tests:${NC}"
for result in "${test_results[@]}"; do
    echo "  $result"
done

echo ""
echo -e "${BLUE}📊 Statistiques:${NC}"
echo "  Total des tests: $total_tests"
echo "  Tests réussis: $((total_tests - failed_tests))"
echo "  Tests échoués: $failed_tests"

if [ $failed_tests -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 SUCCÈS: Tous les tests sont passés!${NC}"
    echo -e "${GREEN}✅ Le déploiement NafaPlace est opérationnel${NC}"
    
    echo ""
    echo -e "${CYAN}🌐 URLs de production:${NC}"
    echo "  - API Gateway: https://nafaplace-api-gateway$APP_SUFFIX.fly.dev"
    echo "  - Health Check: https://nafaplace-api-gateway$APP_SUFFIX.fly.dev/health"
    
    exit 0
else
    echo ""
    echo -e "${RED}❌ ÉCHEC: $failed_tests test(s) ont échoué${NC}"
    echo -e "${RED}🔧 Vérifiez les logs et corrigez les problèmes${NC}"
    
    echo ""
    echo -e "${YELLOW}🔍 Commandes de diagnostic:${NC}"
    echo "  - flyctl logs --app nafaplace-api-gateway$APP_SUFFIX"
    echo "  - flyctl status --app nafaplace-api-gateway$APP_SUFFIX"
    echo "  - ./scripts/collect-logs.sh $ENVIRONMENT"
    
    exit 1
fi
