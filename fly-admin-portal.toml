# Configuration pour le portail Admin
app = "nafaplace-admin-portal"
primary_region = "cdg"

[build]
  dockerfile = "src/Web/AdminPortal/Dockerfile"

[env]
  ASPNETCORE_ENVIRONMENT = "Production"
  TZ = "Africa/Conakry"
  LANG = "fr_GN.UTF-8"

[http_service]
  internal_port = 80
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1

[[http_service.checks]]
  grace_period = "15s"
  interval = "30s"
  method = "GET"
  timeout = "10s"
  path = "/"

[[vm]]
  memory = "512mb"
  cpu_kind = "shared"
  cpus = 1

[deploy]
  strategy = "rolling"
