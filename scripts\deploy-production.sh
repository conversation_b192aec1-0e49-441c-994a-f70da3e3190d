#!/bin/bash

# Script de déploiement fiable pour NafaPlace en production
# Auteur: NafaPlace Team
# Version: 2.0

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Paramètres
SKIP_TESTS=false
FORCE=false
ENVIRONMENT="production"

# Traitement des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        *)
            echo "Usage: $0 [--skip-tests] [--force] [--environment production|staging]"
            exit 1
            ;;
    esac
done

echo -e "${GREEN}🚀 Déploiement NafaPlace en Production${NC}"
echo -e "${YELLOW}Environment: $ENVIRONMENT${NC}"
echo -e "${YELLOW}Skip Tests: $SKIP_TESTS${NC}"
echo -e "${YELLOW}Force Deploy: $FORCE${NC}"
echo ""

# Vérification des prérequis
echo -e "${BLUE}🔍 Vérification des prérequis...${NC}"

# Vérifier flyctl
if command -v flyctl &> /dev/null; then
    FLY_VERSION=$(flyctl version)
    echo -e "${GREEN}✅ Fly CLI: $FLY_VERSION${NC}"
else
    echo -e "${RED}❌ Fly CLI non trouvé. Installez-le depuis https://fly.io/docs/getting-started/installing-flyctl/${NC}"
    exit 1
fi

# Vérifier l'authentification Fly
if flyctl auth whoami &> /dev/null; then
    FLY_AUTH=$(flyctl auth whoami)
    echo -e "${GREEN}✅ Authentifié sur Fly.io: $FLY_AUTH${NC}"
else
    echo -e "${RED}❌ Non authentifié sur Fly.io. Exécutez: flyctl auth login${NC}"
    exit 1
fi

# Tests (si non ignorés)
if [ "$SKIP_TESTS" = false ]; then
    echo -e "${BLUE}🧪 Exécution des tests...${NC}"
    if dotnet test --configuration Release --verbosity minimal; then
        echo -e "${GREEN}✅ Tests réussis${NC}"
    else
        echo -e "${RED}❌ Tests échoués${NC}"
        if [ "$FORCE" = false ]; then
            exit 1
        fi
        echo -e "${YELLOW}⚠️ Déploiement forcé malgré les tests échoués${NC}"
    fi
fi

# Ordre de déploiement des services
declare -a services=(
    "Identity API:fly-identity.toml:nafaplace-identity-api"
    "Catalog API:fly-catalog.toml:nafaplace-catalog-api"
    "Cart API:fly-cart.toml:nafaplace-cart-api"
    "Order API:fly-order.toml:nafaplace-order-api"
    "Payment API:fly-payment.toml:nafaplace-payment-api"
    "Reviews API:fly-reviews.toml:nafaplace-reviews-api"
    "Notifications API:fly-notifications.toml:nafaplace-notifications-api"
    "API Gateway:fly.toml:nafaplace-api-gateway"
    "Web App:fly-web.toml:nafaplace-web"
    "Admin Portal:fly-admin-portal.toml:nafaplace-admin-portal"
    "Seller Portal:fly-seller-portal.toml:nafaplace-seller-portal"
)

deployed_services=()
failed_services=()

# Déploiement des services
echo -e "${BLUE}🚀 Déploiement des services...${NC}"

for service_info in "${services[@]}"; do
    IFS=':' read -r service_name config_file app_name <<< "$service_info"
    
    echo ""
    echo -e "${CYAN}📦 Déploiement de $service_name...${NC}"
    
    if flyctl deploy --config "$config_file" --app "$app_name" --wait-timeout 300; then
        # Vérification de santé
        echo -e "${YELLOW}🏥 Vérification de santé de $service_name...${NC}"
        sleep 10
        
        if flyctl status --app "$app_name" | grep -q "started"; then
            echo -e "${GREEN}✅ $service_name déployé avec succès${NC}"
            deployed_services+=("$service_name")
        else
            echo -e "${RED}❌ $service_name non démarré${NC}"
            failed_services+=("$service_name")
            
            if [ "$FORCE" = false ]; then
                echo -e "${YELLOW}🔄 Rollback des services déployés...${NC}"
                exit 1
            fi
        fi
    else
        echo -e "${RED}❌ Échec du déploiement de $service_name${NC}"
        failed_services+=("$service_name")
        
        if [ "$FORCE" = false ]; then
            echo -e "${YELLOW}🔄 Rollback des services déployés...${NC}"
            exit 1
        fi
    fi
done

# Résumé du déploiement
echo ""
echo -e "${BLUE}📊 Résumé du déploiement:${NC}"
echo -e "${GREEN}✅ Services déployés avec succès: ${#deployed_services[@]}${NC}"
for service in "${deployed_services[@]}"; do
    echo -e "${GREEN}  - $service${NC}"
done

if [ ${#failed_services[@]} -gt 0 ]; then
    echo -e "${RED}❌ Services échoués: ${#failed_services[@]}${NC}"
    for service in "${failed_services[@]}"; do
        echo -e "${RED}  - $service${NC}"
    done
fi

# Tests post-déploiement
echo ""
echo -e "${BLUE}🔍 Tests post-déploiement...${NC}"

endpoints=(
    "https://nafaplace-api-gateway.fly.dev/health"
    "https://nafaplace-web.fly.dev/"
)

for endpoint in "${endpoints[@]}"; do
    if curl -s --max-time 30 "$endpoint" > /dev/null; then
        echo -e "${GREEN}✅ $endpoint - OK${NC}"
    else
        echo -e "${RED}❌ $endpoint - Erreur${NC}"
    fi
done

echo ""
if [ ${#failed_services[@]} -eq 0 ]; then
    echo -e "${GREEN}🎉 Déploiement terminé avec succès!${NC}"
    echo -e "${CYAN}🌐 Application disponible sur: https://nafaplace-web.fly.dev${NC}"
else
    echo -e "${YELLOW}⚠️ Déploiement terminé avec des erreurs${NC}"
    exit 1
fi
