#!/bin/bash

# Script de configuration des bases de données PostgreSQL pour NafaPlace en production
# Auteur: NafaPlace Team
# Version: 2.0

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Paramètres
ENVIRONMENT=${1:-production}
REGION=${2:-cdg}

if [ "$ENVIRONMENT" = "staging" ]; then
    APP_SUFFIX="-staging"
else
    APP_SUFFIX=""
fi

echo -e "${GREEN}🗄️ Configuration des bases de données PostgreSQL pour NafaPlace${NC}"
echo -e "${YELLOW}Environment: $ENVIRONMENT${NC}"
echo -e "${YELLOW}Region: $REGION${NC}"
echo ""

# Vérifier flyctl
if ! command -v flyctl &> /dev/null; then
    echo -e "${RED}❌ Fly CLI non trouvé. Installez-le depuis https://fly.io/docs/getting-started/installing-flyctl/${NC}"
    exit 1
fi

# Vérifier l'authentification
if ! flyctl auth whoami &> /dev/null; then
    echo -e "${RED}❌ Non authentifié sur Fly.io. Exécutez: flyctl auth login${NC}"
    exit 1
fi

echo -e "${BLUE}🏗️ Création des bases de données PostgreSQL...${NC}"

# Liste des bases de données à créer
databases=(
    "nafaplace-identity-db$APP_SUFFIX:NafaPlace.Identity"
    "nafaplace-catalog-db$APP_SUFFIX:NafaPlace.Catalog"
    "nafaplace-order-db$APP_SUFFIX:NafaPlace.Order"
    "nafaplace-reviews-db$APP_SUFFIX:NafaPlace.Reviews"
    "nafaplace-notifications-db$APP_SUFFIX:NafaPlace.Notifications"
)

# Fonction pour créer une base de données
create_database() {
    local app_name=$1
    local db_name=$2
    
    echo -e "${CYAN}🔧 Création de $app_name...${NC}"
    
    # Vérifier si l'application existe déjà
    if flyctl apps list | grep -q "$app_name"; then
        echo -e "${YELLOW}⚠️ $app_name existe déjà${NC}"
    else
        # Créer l'application PostgreSQL
        if flyctl postgres create \
            --name "$app_name" \
            --region "$REGION" \
            --vm-size shared-cpu-1x \
            --volume-size 10 \
            --initial-cluster-size 1; then
            echo -e "${GREEN}✅ $app_name créée avec succès${NC}"
        else
            echo -e "${RED}❌ Échec de création de $app_name${NC}"
            return 1
        fi
    fi
    
    # Attendre que la base soit prête
    echo -e "${YELLOW}⏳ Attente que $app_name soit prête...${NC}"
    sleep 30
    
    # Configurer les backups automatiques
    echo -e "${CYAN}💾 Configuration des backups pour $app_name...${NC}"
    flyctl postgres config update \
        --app "$app_name" \
        --max-connections 100 \
        --shared-preload-libraries "pg_stat_statements" || true
    
    echo -e "${GREEN}✅ $app_name configurée${NC}"
}

# Créer toutes les bases de données
for db_info in "${databases[@]}"; do
    IFS=':' read -r app_name db_name <<< "$db_info"
    create_database "$app_name" "$db_name"
    echo ""
done

echo -e "${BLUE}🔗 Configuration des connexions...${NC}"

# Fonction pour obtenir l'URL de connexion
get_connection_url() {
    local app_name=$1
    flyctl postgres connect --app "$app_name" --database postgres --command "SELECT 1;" > /dev/null 2>&1 || true
    flyctl postgres connect --app "$app_name" --database postgres --command "\l" > /dev/null 2>&1 || true
}

# Tester les connexions
for db_info in "${databases[@]}"; do
    IFS=':' read -r app_name db_name <<< "$db_info"
    echo -e "${CYAN}🔍 Test de connexion à $app_name...${NC}"
    
    if flyctl status --app "$app_name" | grep -q "started"; then
        echo -e "${GREEN}✅ $app_name est opérationnelle${NC}"
    else
        echo -e "${RED}❌ $app_name n'est pas démarrée${NC}"
    fi
done

echo ""
echo -e "${GREEN}✅ Configuration des bases de données terminée!${NC}"
echo ""
echo -e "${BLUE}📋 Résumé des bases de données créées:${NC}"
for db_info in "${databases[@]}"; do
    IFS=':' read -r app_name db_name <<< "$db_info"
    echo -e "${CYAN}  - $app_name ($db_name)${NC}"
done

echo ""
echo -e "${BLUE}📋 Prochaines étapes:${NC}"
echo "1. Vérifiez le statut: flyctl status --app [db-app-name]"
echo "2. Configurez les secrets: ./scripts/setup-fly-secrets-v2.sh $ENVIRONMENT"
echo "3. Déployez les APIs: ./scripts/deploy-production.sh"

echo ""
echo -e "${YELLOW}⚠️ Notes importantes:${NC}"
echo "- Les backups automatiques sont configurés"
echo "- Les bases utilisent des volumes de 10GB (extensibles)"
echo "- Les connexions sont limitées à 100 par défaut"
echo "- Surveillez l'utilisation des ressources après déploiement"

echo ""
echo -e "${CYAN}🔐 Sécurité:${NC}"
echo "- Accès restreint aux applications autorisées"
echo "- Chiffrement en transit et au repos"
echo "- Mots de passe générés automatiquement"
