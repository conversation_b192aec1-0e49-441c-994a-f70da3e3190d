# 🚀 Guide de Déploiement NafaPlace v2.0

## 📋 Vue d'ensemble

Ce guide décrit le processus de déploiement fiable de NafaPlace sur Fly.io avec toutes les améliorations de sécurité et de stabilité.

## 🔧 Prérequis

### 1. Outils requis
- [Fly CLI](https://fly.io/docs/getting-started/installing-flyctl/) installé
- Docker installé et fonctionnel
- Git configuré
- PowerShell (Windows) ou Bash (Linux/macOS)

### 2. Variables d'environnement
Configurez ces variables avant le déploiement :

```bash
export STRIPE_SECRET_KEY="sk_live_..."
export STRIPE_WEBHOOK_SECRET="whsec_..."
export CLOUDINARY_URL="cloudinary://api_key:api_secret@cloud_name"
```

### 3. Authentification Fly.io
```bash
flyctl auth login
```

## 🏗️ Architecture de Déploiement

### Services déployés :
1. **Bases de données PostgreSQL** (avec backups automatiques)
2. **APIs de microservices** (Identity, Catalog, Cart, Order, Payment, Reviews, Notifications)
3. **API Gateway** (point d'entrée principal)
4. **Applications Web** (Web, Admin Portal, Seller Portal)

### Ordre de déploiement :
1. Bases de données
2. Services backend (APIs)
3. API Gateway
4. Applications frontend

## 🚀 Processus de Déploiement

### Étape 1: Préparation et Validation
```bash
# Cloner le repository
git clone https://github.com/diakitelamine/nafaplace.git
cd nafaplace

# Configurer les variables d'environnement
export STRIPE_SECRET_KEY="sk_live_..."
export STRIPE_WEBHOOK_SECRET="whsec_..."
export CLOUDINARY_URL="cloudinary://api_key:api_secret@cloud_name"

# Vérifier les configurations
./scripts/validate-production-deployment.ps1 production
```

### Étape 2: Configuration des bases de données
```bash
# Créer et configurer les bases de données PostgreSQL
./scripts/setup-production-databases.sh production
```

### Étape 3: Configuration des secrets
```bash
# Configurer tous les secrets de manière sécurisée
./scripts/setup-fly-secrets-v2.sh production
```

### Étape 4: Déploiement des services
```bash
# Déploiement automatisé avec validation complète
./scripts/deploy-production.sh
```

### Étape 5: Configuration du monitoring
```bash
# Configurer le monitoring et les alertes
./scripts/setup-monitoring.sh production
```

### Étape 6: Test complet
```bash
# Effectuer un test complet du déploiement
./scripts/test-complete-deployment.sh production
```

## 🔍 Validation du Déploiement

### Tests automatiques
Le script de déploiement inclut :
- ✅ Tests de santé pour chaque service
- ✅ Validation des endpoints
- ✅ Vérification de la connectivité entre services

### Tests manuels
```bash
# Vérifier le statut des applications
flyctl apps list

# Vérifier les logs
flyctl logs --app nafaplace-api-gateway

# Tester les endpoints
curl https://nafaplace-api-gateway.fly.dev/health
curl https://nafaplace-web.fly.dev/
```

## 🛠️ Résolution des Problèmes

### Problèmes courants

#### 1. Health checks échouent
```bash
# Vérifier les logs
flyctl logs --app [app-name]

# Redémarrer l'application
flyctl apps restart [app-name]
```

#### 2. Problèmes de base de données
```bash
# Vérifier la connectivité
flyctl ssh console --app [db-app-name]

# Vérifier les migrations
flyctl ssh console --app [api-app-name]
```

#### 3. Problèmes de secrets
```bash
# Lister les secrets
flyctl secrets list --app [app-name]

# Reconfigurer les secrets
./scripts/setup-fly-secrets-v2.sh production
```

## 📊 Monitoring et Maintenance

### Surveillance
- **Logs centralisés** : `flyctl logs --app [app-name]`
- **Métriques** : Dashboard Fly.io
- **Alertes** : Configuration via Fly.io

### Backups
- **Bases de données** : Backups automatiques quotidiens
- **Configuration** : Sauvegardée dans Git

### Mises à jour
```bash
# Déploiement avec tests
./scripts/deploy-production.sh

# Rollback si nécessaire
flyctl releases list --app [app-name]
flyctl releases rollback [version] --app [app-name]
```

## 🔐 Sécurité

### Mesures implémentées :
- ✅ Secrets chiffrés avec Fly.io
- ✅ HTTPS forcé sur tous les services
- ✅ JWT avec clés rotatives
- ✅ Isolation réseau entre services
- ✅ Scans de sécurité automatiques

### Bonnes pratiques :
- Rotation régulière des secrets
- Monitoring des accès
- Mises à jour de sécurité automatiques

## 📞 Support

### En cas de problème :
1. Vérifier les logs : `flyctl logs --app [app-name]`
2. Consulter le statut : `flyctl status --app [app-name]`
3. Redémarrer si nécessaire : `flyctl apps restart [app-name]`

### Contacts :
- **Équipe technique** : [email]
- **Documentation** : [lien vers docs]
- **Issues GitHub** : [lien vers issues]

## 🎯 URLs de Production

- **Application principale** : https://nafaplace-web.fly.dev
- **API Gateway** : https://nafaplace-api-gateway.fly.dev
- **Admin Portal** : https://nafaplace-admin-portal.fly.dev
- **Seller Portal** : https://nafaplace-seller-portal.fly.dev

---

**Version** : 2.0  
**Dernière mise à jour** : 2025-07-03  
**Auteur** : Équipe NafaPlace
