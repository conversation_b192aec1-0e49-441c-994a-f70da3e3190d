using Microsoft.EntityFrameworkCore;
using Npgsql.EntityFrameworkCore.PostgreSQL;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Domain.Repositories;
using NafaPlace.Identity.Infrastructure.Data;
using NafaPlace.Identity.Infrastructure.Repositories;
using NafaPlace.Identity.Infrastructure.Services;
using System;

namespace NafaPlace.Identity.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Utiliser DATABASE_URL si disponible (pour Fly.io), sinon utiliser DefaultConnection
        var connectionString = Environment.GetEnvironmentVariable("DATABASE_URL")
            ?? configuration.GetConnectionString("DefaultConnection");

        services.AddDbContext<Data.IdentityDbContext>(options =>
            options.UseNpgsql(connectionString));

        services.AddScoped<IUserRepository, UserRepository>();
        
        // Enregistrer les services
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IRoleService, RoleService>();
        services.AddScoped<IAuthService, AuthService>();
        services.AddScoped<IJwtService, JwtService>();
        services.AddScoped<Microsoft.AspNetCore.Identity.IPasswordHasher<Domain.Models.User>, PasswordHasher>();

        return services;
    }
}
