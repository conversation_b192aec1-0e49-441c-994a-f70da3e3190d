param(
    [string]$Environment = "production"
)

Write-Host "🔍 Validation du déploiement NafaPlace" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host ""

$errors = 0

# Vérifier Fly CLI
Write-Host "🔧 Vérification des outils..." -ForegroundColor Blue
try {
    $flyVersion = flyctl version
    Write-Host "✅ Fly CLI installé: $flyVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Fly CLI non trouvé" -ForegroundColor Red
    $errors++
}

# Vérifier Docker
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker installé: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker non trouvé" -ForegroundColor Red
    $errors++
}

# Vérifier .NET
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK installé: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET SDK non trouvé" -ForegroundColor Red
    $errors++
}

# Vérifier l'authentification Fly.io
Write-Host ""
Write-Host "🔐 Vérification de l'authentification..." -ForegroundColor Blue
try {
    $flyAuth = flyctl auth whoami
    Write-Host "✅ Authentifié sur Fly.io: $flyAuth" -ForegroundColor Green
} catch {
    Write-Host "❌ Non authentifié sur Fly.io. Exécutez: flyctl auth login" -ForegroundColor Red
    $errors++
}

# Vérifier les configurations
Write-Host ""
Write-Host "📁 Vérification des configurations..." -ForegroundColor Blue
$configs = @("fly.toml", "fly-catalog.toml", "fly-identity.toml", "fly-cart.toml", "fly-order.toml", "fly-payment.toml")
foreach ($config in $configs) {
    if (Test-Path $config) {
        Write-Host "✅ Configuration trouvée: $config" -ForegroundColor Green
    } else {
        Write-Host "❌ Configuration manquante: $config" -ForegroundColor Red
        $errors++
    }
}

# Vérifier les variables d'environnement
Write-Host ""
Write-Host "🌍 Vérification des variables d'environnement..." -ForegroundColor Blue
$stripeKey = $env:STRIPE_SECRET_KEY
$stripeWebhook = $env:STRIPE_WEBHOOK_SECRET
$cloudinary = $env:CLOUDINARY_URL

if ($stripeKey) {
    Write-Host "✅ STRIPE_SECRET_KEY configurée" -ForegroundColor Green
} else {
    Write-Host "⚠️ STRIPE_SECRET_KEY non configurée (placeholder sera utilisé)" -ForegroundColor Yellow
}

if ($stripeWebhook) {
    Write-Host "✅ STRIPE_WEBHOOK_SECRET configurée" -ForegroundColor Green
} else {
    Write-Host "⚠️ STRIPE_WEBHOOK_SECRET non configurée (placeholder sera utilisé)" -ForegroundColor Yellow
}

if ($cloudinary) {
    Write-Host "✅ CLOUDINARY_URL configurée" -ForegroundColor Green
} else {
    Write-Host "⚠️ CLOUDINARY_URL non configurée (placeholder sera utilisé)" -ForegroundColor Yellow
}

# Résumé
Write-Host ""
Write-Host "📊 Résumé de la validation" -ForegroundColor Blue
Write-Host "=========================" -ForegroundColor Blue

if ($errors -eq 0) {
    Write-Host "🎉 Validation réussie! Prêt pour le déploiement." -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Prochaines étapes:" -ForegroundColor Cyan
    Write-Host "1. ./scripts/setup-production-databases.sh $Environment"
    Write-Host "2. ./scripts/setup-fly-secrets-v2.sh $Environment"
    Write-Host "3. ./scripts/deploy-production.sh"
    exit 0
} else {
    Write-Host "❌ Validation échouée! $errors erreur(s) trouvée(s)." -ForegroundColor Red
    Write-Host "Corrigez les erreurs avant de continuer." -ForegroundColor Red
    exit 1
}
