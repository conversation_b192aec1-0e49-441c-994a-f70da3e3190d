# Configuration pour le service Identity
app = "nafaplace-identity-api"
primary_region = "cdg"

[build]
  dockerfile = "src/Services/Identity/NafaPlace.Identity.API/Dockerfile"

[env]
  ASPNETCORE_ENVIRONMENT = "Production"
  ASPNETCORE_URLS = "http://+:8080"
  DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "false"
  TZ = "Africa/Conakry"
  LANG = "fr_GN.UTF-8"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1

[[http_service.checks]]
  grace_period = "30s"
  interval = "60s"
  method = "GET"
  timeout = "10s"
  path = "/health"

[http_service.concurrency]
  type = "connections"
  hard_limit = 1000
  soft_limit = 800

[[vm]]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1

[deploy]
  strategy = "rolling"
  max_unavailable = 0.33
