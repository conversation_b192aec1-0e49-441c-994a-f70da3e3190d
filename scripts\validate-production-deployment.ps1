#!/usr/bin/env pwsh

# Script de validation du déploiement NafaPlace en production
# Vérifie que tous les prérequis sont en place avant le déploiement

param(
    [string]$Environment = "production"
)

$ErrorActionPreference = "Stop"

Write-Host "🔍 Validation du déploiement NafaPlace" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host ""

$validationErrors = @()
$validationWarnings = @()

# Fonction pour ajouter une erreur
function Add-ValidationError($message) {
    $script:validationErrors += $message
    Write-Host "❌ $message" -ForegroundColor Red
}

# Fonction pour ajouter un avertissement
function Add-ValidationWarning($message) {
    $script:validationWarnings += $message
    Write-Host "⚠️ $message" -ForegroundColor Yellow
}

# Fonction pour valider un succès
function Add-ValidationSuccess($message) {
    Write-Host "✅ $message" -ForegroundColor Green
}

Write-Host "🔧 Vérification des outils..." -ForegroundColor Blue

# Vérifier Fly CLI
try {
    $flyVersion = flyctl version
    Add-ValidationSuccess "Fly CLI installé: $flyVersion"
} catch {
    Add-ValidationError "Fly CLI non trouvé. Installez-le depuis https://fly.io/docs/getting-started/installing-flyctl/"
}

# Vérifier Docker
try {
    $dockerVersion = docker --version
    Add-ValidationSuccess "Docker installé: $dockerVersion"
} catch {
    Add-ValidationError "Docker non trouvé. Installez Docker Desktop"
}

# Vérifier Git
try {
    $gitVersion = git --version
    Add-ValidationSuccess "Git installé: $gitVersion"
} catch {
    Add-ValidationError "Git non trouvé. Installez Git"
}

# Vérifier .NET SDK
try {
    $dotnetVersion = dotnet --version
    Add-ValidationSuccess ".NET SDK installé: $dotnetVersion"
} catch {
    Add-ValidationError ".NET SDK non trouvé. Installez .NET 9.0 SDK"
}

Write-Host ""
Write-Host "🔐 Vérification de l'authentification..." -ForegroundColor Blue

# Vérifier l'authentification Fly.io
try {
    $flyAuth = flyctl auth whoami
    Add-ValidationSuccess "Authentifié sur Fly.io: $flyAuth"
} catch {
    Add-ValidationError "Non authentifié sur Fly.io. Exécutez: flyctl auth login"
}

Write-Host ""
Write-Host "📁 Vérification des fichiers de configuration..." -ForegroundColor Blue

# Vérifier les fichiers fly.toml
$flyConfigs = @(
    "fly.toml",
    "fly-catalog.toml",
    "fly-identity.toml",
    "fly-cart.toml",
    "fly-order.toml",
    "fly-payment.toml",
    "fly-reviews.toml",
    "fly-notifications.toml"
)

foreach ($config in $flyConfigs) {
    if (Test-Path $config) {
        Add-ValidationSuccess "Configuration trouvée: $config"
    } else {
        Add-ValidationError "Configuration manquante: $config"
    }
}

Write-Host ""
Write-Host "🌍 Vérification des variables d'environnement..." -ForegroundColor Blue

# Vérifier les variables d'environnement importantes
$envVars = @(
    "STRIPE_SECRET_KEY",
    "STRIPE_WEBHOOK_SECRET",
    "CLOUDINARY_URL"
)

foreach ($envVar in $envVars) {
    $value = [Environment]::GetEnvironmentVariable($envVar)
    if ($value) {
        if ($value.StartsWith("sk_live_") -or $value.StartsWith("sk_test_") -or $value.StartsWith("whsec_") -or $value.StartsWith("cloudinary://")) {
            Add-ValidationSuccess "$envVar configurée correctement"
        } else {
            Add-ValidationWarning "$envVar configurée mais format suspect"
        }
    } else {
        Add-ValidationWarning "$envVar non configurée (sera utilisée une valeur placeholder)"
    }
}

Write-Host ""
Write-Host "🏗️ Vérification de la compilation..." -ForegroundColor Blue

# Tester la compilation
try {
    Write-Host "Compilation en cours..." -ForegroundColor Yellow
    dotnet build --configuration Release --verbosity quiet
    Add-ValidationSuccess "Compilation réussie"
} catch {
    Add-ValidationError "Échec de compilation: $($_.Exception.Message)"
}

Write-Host ""
Write-Host "📊 Résumé de la validation" -ForegroundColor Blue
Write-Host "=========================" -ForegroundColor Blue

if ($validationErrors.Count -eq 0) {
    Write-Host "🎉 Validation réussie! Prêt pour le déploiement." -ForegroundColor Green
    
    if ($validationWarnings.Count -gt 0) {
        Write-Host ""
        Write-Host "⚠️ Avertissements ($($validationWarnings.Count)):" -ForegroundColor Yellow
        foreach ($warning in $validationWarnings) {
            Write-Host "  - $warning" -ForegroundColor Yellow
        }
    }
    
    Write-Host ""
    Write-Host "📋 Prochaines étapes recommandées:" -ForegroundColor Cyan
    Write-Host "1. ./scripts/setup-production-databases.sh $Environment"
    Write-Host "2. ./scripts/setup-fly-secrets-v2.sh $Environment"
    Write-Host "3. ./scripts/deploy-production.sh"
    
    exit 0
} else {
    Write-Host "❌ Validation échouée! Corrigez les erreurs avant de déployer." -ForegroundColor Red
    Write-Host ""
    Write-Host "Erreurs ($($validationErrors.Count)):" -ForegroundColor Red
    foreach ($error in $validationErrors) {
        Write-Host "  - $error" -ForegroundColor Red
    }
    
    if ($validationWarnings.Count -gt 0) {
        Write-Host ""
        Write-Host "Avertissements ($($validationWarnings.Count)):" -ForegroundColor Yellow
        foreach ($warning in $validationWarnings) {
            Write-Host "  - $warning" -ForegroundColor Yellow
        }
    }
    
    exit 1
}
