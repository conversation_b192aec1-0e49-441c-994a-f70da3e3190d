#!/bin/bash

# Script de configuration des secrets Fly.io pour NafaPlace
# Auteur: NafaPlace Team
# Version: 2.0 - Amélioré avec validation et gestion d'erreurs

set -e

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Paramètres
ENVIRONMENT=${1:-production}
FORCE=${2:-false}

if [ "$ENVIRONMENT" = "staging" ]; then
    APP_SUFFIX="-staging"
else
    APP_SUFFIX=""
fi

echo -e "${GREEN}🔐 Configuration des secrets Fly.io pour NafaPlace${NC}"
echo -e "${YELLOW}Environment: $ENVIRONMENT${NC}"
echo -e "${YELLOW}Force: $FORCE${NC}"
echo ""

# Vérifier flyctl
if ! command -v flyctl &> /dev/null; then
    echo -e "${RED}❌ Fly CLI non trouvé. Installez-le depuis https://fly.io/docs/getting-started/installing-flyctl/${NC}"
    exit 1
fi

# Vérifier l'authentification
if ! flyctl auth whoami &> /dev/null; then
    echo -e "${RED}❌ Non authentifié sur Fly.io. Exécutez: flyctl auth login${NC}"
    exit 1
fi

# Vérifier les variables d'environnement requises
if [ -z "$STRIPE_SECRET_KEY" ] && [ "$FORCE" != "true" ]; then
    echo -e "${YELLOW}⚠️ STRIPE_SECRET_KEY non définie. Utilisation d'une valeur placeholder${NC}"
    STRIPE_SECRET_KEY="sk_test_placeholder"
fi

if [ -z "$STRIPE_WEBHOOK_SECRET" ] && [ "$FORCE" != "true" ]; then
    echo -e "${YELLOW}⚠️ STRIPE_WEBHOOK_SECRET non définie. Utilisation d'une valeur placeholder${NC}"
    STRIPE_WEBHOOK_SECRET="whsec_placeholder"
fi

if [ -z "$CLOUDINARY_URL" ] && [ "$FORCE" != "true" ]; then
    echo -e "${YELLOW}⚠️ CLOUDINARY_URL non définie. Utilisation d'une valeur placeholder${NC}"
    CLOUDINARY_URL="cloudinary://api_key:api_secret@cloud_name"
fi

echo -e "${BLUE}🔑 Configuration des secrets...${NC}"

# Générer des clés sécurisées
JWT_SECRET=$(openssl rand -base64 32)
DB_PASSWORD="NafaPlace2025@Prod$(openssl rand -base64 8)"
ENCRYPTION_KEY=$(openssl rand -base64 32)

# Configuration des secrets par service
configure_secrets() {
    local app_name=$1
    shift
    local secrets=("$@")
    
    echo -e "${CYAN}🔧 Configuration de $app_name...${NC}"
    
    if flyctl apps list | grep -q "$app_name"; then
        local secret_args=""
        for secret in "${secrets[@]}"; do
            secret_args="$secret_args $secret"
        done
        
        if eval "flyctl secrets set --app \"$app_name\" $secret_args"; then
            echo -e "${GREEN}✅ Secrets configurés pour $app_name${NC}"
        else
            echo -e "${RED}❌ Échec de configuration pour $app_name${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️ Application $app_name non trouvée, ignorée${NC}"
    fi
}

# Secrets pour l'API Gateway
configure_secrets "nafaplace-api-gateway$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$JWT_SECRET\"" \
  "ENCRYPTION_KEY=\"$ENCRYPTION_KEY\""

# Secrets pour Identity API
configure_secrets "nafaplace-identity-api$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$JWT_SECRET\"" \
  "ENCRYPTION_KEY=\"$ENCRYPTION_KEY\"" \
  "ConnectionStrings__DefaultConnection=\"Host=nafaplace-identity-db$APP_SUFFIX.internal:5432;Database=NafaPlace.Identity;Username=postgres;Password=$DB_PASSWORD\""

# Secrets pour Catalog API
configure_secrets "nafaplace-catalog-api$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$JWT_SECRET\"" \
  "ConnectionStrings__DefaultConnection=\"Host=nafaplace-catalog-db$APP_SUFFIX.internal:5432;Database=NafaPlace.Catalog;Username=postgres;Password=$DB_PASSWORD\"" \
  "CLOUDINARY_URL=\"$CLOUDINARY_URL\""

# Secrets pour Cart API
configure_secrets "nafaplace-cart-api$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$JWT_SECRET\""

# Secrets pour Order API
configure_secrets "nafaplace-order-api$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$JWT_SECRET\"" \
  "ConnectionStrings__DefaultConnection=\"Host=nafaplace-order-db$APP_SUFFIX.internal:5432;Database=NafaPlace.Order;Username=postgres;Password=$DB_PASSWORD\""

# Secrets pour Payment API
configure_secrets "nafaplace-payment-api$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$JWT_SECRET\"" \
  "STRIPE_SECRET_KEY=\"$STRIPE_SECRET_KEY\"" \
  "STRIPE_WEBHOOK_SECRET=\"$STRIPE_WEBHOOK_SECRET\""

# Secrets pour Reviews API
configure_secrets "nafaplace-reviews-api$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$JWT_SECRET\"" \
  "ConnectionStrings__DefaultConnection=\"Host=nafaplace-reviews-db$APP_SUFFIX.internal:5432;Database=NafaPlace.Reviews;Username=postgres;Password=$DB_PASSWORD\""

# Secrets pour Notifications API
configure_secrets "nafaplace-notifications-api$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$JWT_SECRET\"" \
  "ConnectionStrings__DefaultConnection=\"Host=nafaplace-notifications-db$APP_SUFFIX.internal:5432;Database=NafaPlace.Notifications;Username=postgres;Password=$DB_PASSWORD\""

# Secrets pour l'application Web
configure_secrets "nafaplace-web$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$(openssl rand -base64 32)\""

# Secrets pour Admin Portal
configure_secrets "nafaplace-admin-portal$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$JWT_SECRET\""

# Secrets pour Seller Portal
configure_secrets "nafaplace-seller-portal$APP_SUFFIX" \
  "JWT_SECRET_KEY=\"$JWT_SECRET\""

echo ""
echo -e "${GREEN}✅ Configuration des secrets terminée pour $ENVIRONMENT${NC}"
echo ""
echo -e "${BLUE}📋 Prochaines étapes:${NC}"
echo "1. Vérifiez les secrets: flyctl secrets list --app nafaplace-api-gateway$APP_SUFFIX"
echo "2. Déployez les applications: ./scripts/deploy-production.sh"
echo "3. Vérifiez les logs: flyctl logs --app nafaplace-api-gateway$APP_SUFFIX"
echo ""
echo -e "${CYAN}🔐 Informations importantes:${NC}"
echo "- JWT Secret: [GÉNÉRÉ AUTOMATIQUEMENT]"
echo "- DB Password: [GÉNÉRÉ AUTOMATIQUEMENT]"
echo "- Encryption Key: [GÉNÉRÉ AUTOMATIQUEMENT]"
echo ""
echo -e "${YELLOW}⚠️ N'oubliez pas de configurer les vraies valeurs pour:${NC}"
echo "- STRIPE_SECRET_KEY"
echo "- STRIPE_WEBHOOK_SECRET"
echo "- CLOUDINARY_URL"
